<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.audit.mapper.NetValueOfAccountSetMapper">

    <resultMap id="BaseResultMap" type="cn.sdata.om.al.audit.entity.NetValueOfAccountSet">
            <result property="id" column="id" jdbcType="VARCHAR"/>
            <result property="dataDate" column="data_date" jdbcType="VARCHAR"/>
            <result property="productId" column="product_id" jdbcType="VARCHAR"/>
            <result property="productName" column="product_name" jdbcType="VARCHAR"/>
            <result property="structuredName" column="structured_name" jdbcType="VARCHAR"/>
            <result property="enJjzfe" column="en_jjzfe" jdbcType="VARCHAR"/>
            <result property="enJjzjz" column="en_jjzjz" jdbcType="VARCHAR"/>
            <result property="enJjdwjz" column="en_jjdwjz" jdbcType="VARCHAR"/>
            <result property="enLjjz" column="en_ljjz" jdbcType="VARCHAR"/>
            <result property="enDwjjsy" column="en_dwjjsy" jdbcType="VARCHAR"/>
            <result property="enFdsy" column="en_fdsy" jdbcType="VARCHAR"/>
            <result property="drce" column="drce" jdbcType="VARCHAR"/>
            <result property="drzdf" column="drzdf" jdbcType="VARCHAR"/>
            <result property="dwjzYear" column="dwjz_year" jdbcType="VARCHAR"/>
            <result property="ljdwjzYear" column="ljdwjz_year" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,data_date,product_id,
        product_name,structured_name,en_jjzfe,
        en_jjzjz,en_jjdwjz,en_ljjz,
        en_dwjjsy,en_fdsy,drce,
        drzdf,dwjz_year,ljdwjz_year
    </sql>
    <select id="selectNetValueFluctuation" resultType="cn.sdata.om.al.vo.NetValueOfAccountSetVO">
        select
            t.D_RQ dataDate,
            ai.product_code productId,
            ai.full_product_name productName,
            t.VC_JJMC structuredName,
            t.EN_JJZFE enJjzfe,
            t.EN_JJZJZ enJjzjz,
            t.EN_JJDWJZ enJjdwjz,
            t.EN_LJJZ enLjjz,
            t.EN_DWJJSY enDwjjsy,
            t.EN_NSYL enFdsy,
            ai.valuation_time valuationTime
        from
            tjjjz t
                left join (  select * from account_information  where full_product_name not like '%I9' ) ai on
                ai.product_code = t.VC_JJDM
        where
            D_RQ = #{today} and ai.product_category = 3
    </select>
    <select id="selectLastYearNetValue" resultType="cn.sdata.om.al.vo.NetValueOfAccountSetVO">
        SELECT t.VC_JJDM productId,
        DATE_FORMAT(t.D_RQ, '%Y') AS dataDate,
        t.EN_LJJZ enLjjz,
        t.EN_JJDWJZ enJjdwjz
        FROM tjjjz t
        JOIN (
        SELECT VC_JJDM,
        DATE_FORMAT(D_RQ, '%Y') AS year,
        MAX(D_RQ) AS max_date
        FROM tjjjz
        WHERE D_RQ &lt;= CURDATE()
        GROUP BY VC_JJDM, DATE_FORMAT(D_RQ, '%Y')
        ) latest
        ON t.VC_JJDM = latest.VC_JJDM
        AND DATE_FORMAT(t.D_RQ, '%Y') = latest.year
        AND t.D_RQ = latest.max_date
        WHERE DATE_FORMAT(t.D_RQ, '%Y') = #{today}
        ORDER BY t.VC_JJDM , t.D_RQ;
    </select>
    <select id="getActualNumberOfDaysInIntervalTime" resultType="java.lang.Integer">
        SELECT
            COUNT(DISTINCT D_RQ) AS date_count
        FROM
            tjjjz
        WHERE
        D_RQ BETWEEN #{startDay} AND #{today}
    </select>
    <select id="selectPageInfo" resultType="cn.sdata.om.al.vo.NetValueOfAccountSetVO">
        select
        t.id,
        data_date,
        ai.id as product_id,
        ai.product_code as productCode,
        product_name,
        structured_name,
        en_jjzfe,
        en_jjzjz,
        en_jjdwjz,
        en_ljjz,
        en_dwjjsy,
        en_fdsy,
        drce,
        drzdf,
        dwjz_year,
        ljdwjz_year,
        ai.valuation_time,
        trade_day.c_trade_flag as dateType
        from
        net_value_of_account_set t
        left join (  select * from account_information  where full_product_name not like '%I9') ai on   ai.product_code  = t.product_id
        left join (SELECT
        DATE_FORMAT(STR_TO_DATE(l_date, '%Y%m%d'), '%Y-%m-%d') AS l_date ,
        c_trade_flag
        FROM market_trade_day
        WHERE vc_tradeday_type = '02') trade_day on   trade_day.l_date  = t.data_date
        <where> ai.product_category = 3
            <if test="vo.startDate != null and vo.startDate != ''">
                and data_date &gt;= #{vo.startDate}
            </if>
            <if test="vo.endDate != null and vo.endDate != ''">
                and data_date &lt;= #{vo.endDate}
            </if>
            <if test="vo.productNames != null and vo.productNames.size() > 0">
                and product_name in
                <foreach collection="vo.productNames" open="(" separator="," item="name" close=")">
                    #{name}
                </foreach>
            </if>
            <if test="vo.productCodes != null and vo.productCodes.size() > 0">
                and t.product_id in
                <foreach collection="vo.productCodes" open="(" separator="," item="code" close=")">
                    #{code}
                </foreach>
            </if>
            <if test="vo.productGroups != null and vo.productGroups.size() > 0">
                and ai.id in
                <foreach collection="vo.productGroups" open="(" separator="," item="code" close=")">
                    #{code}
                </foreach>
            </if>
            <if test="vo.valuationTime != null and vo.valuationTime != ''">
                and ai.valuation_time = #{vo.valuationTime}
            </if>
            <if test="vo.dateType != null and vo.dateType != ''">
                and trade_day.c_trade_flag = #{vo.dateType}
            </if>
        </where>
        order by data_date desc, structured_name desc
    </select>
    <select id="selectProductGroups" resultType="java.lang.String">
        select account_code from account_set_group
        where
        id in
        <foreach collection="productGroups" open="(" separator="," item="code" close=")">
            #{code}
        </foreach>
    </select>
</mapper>
