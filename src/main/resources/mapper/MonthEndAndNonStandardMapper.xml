<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.audit.mapper.MonthEndAndNonStandardMapper">

    <resultMap id="BaseResultMap" type="cn.sdata.om.al.audit.entity.MonthEndAndNonStandard">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="dataDate" column="data_date" jdbcType="VARCHAR"/>
            <result property="productId" column="product_id" jdbcType="VARCHAR"/>
            <result property="productName" column="product_name" jdbcType="VARCHAR"/>
            <result property="securityType" column="security_type" jdbcType="VARCHAR"/>
            <result property="securityName" column="security_name" jdbcType="VARCHAR"/>
            <result property="securityCode" column="security_code" jdbcType="VARCHAR"/>
            <result property="securityCost" column="security_cost" jdbcType="VARCHAR"/>
            <result property="securityMarketValue" column="security_market_value" jdbcType="VARCHAR"/>
            <result property="roi" column="ROI" jdbcType="VARCHAR"/>
            <result property="proportionPrompt" column="proportion_prompt" jdbcType="VARCHAR"/>
            <result property="monthEndValuationPrice" column="month_end_valuation_price" jdbcType="VARCHAR"/>
            <result property="valuationPriceEndLastMonth" column="valuation_price_end_last_month" jdbcType="VARCHAR"/>
            <result property="priceVolatility" column="price_volatility" jdbcType="VARCHAR"/>
            <result property="volatilityAlert" column="volatility_alert" jdbcType="VARCHAR"/>
            <result property="interestPaymentDate" column="interest_payment_date" jdbcType="VARCHAR"/>
            <result property="exerciseDate" column="exercise_date" jdbcType="VARCHAR"/>
            <result property="dueDate" column="due_date" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,data_date,product_id,
        product_name,security_type,security_name,
        security_code,security_cost,security_market_value,
        ROI,proportion_prompt,month_end_valuation_price,
        valuation_price_end_last_month,price_volatility,volatility_alert,
        interest_payment_date,exercise_date,due_date
    </sql>
    <select id="selectPageInfo" resultType="cn.sdata.om.al.vo.MonthEndAndNonStandardVO">

    </select>
</mapper>
