package cn.sdata.om.al.vo;

import cn.sdata.om.al.entity.PageParam;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 月末存款及非标行情检查
 */
@Data
public class MonthEndAndNonStandardVO extends PageParam {
    /**
     * id
     */
    private String id;

    /**
     * 数据日期
     */
    private String dataDate;

    /**
     * 账套编号
     */
    private String productId;

    /**
     * 账套名称
     */
    private String productName;

    /**
     * 证券类型
     */
    private String securityType;

    /**
     * 证券名称
     */
    private String securityName;

    /**
     * 证券代码
     */
    private String securityCode;

    /**
     * 证券成本
     */
    private String securityCost;

    /**
     * 证券市值
     */
    private String securityMarketValue;

    /**
     * 估值与本金的比例
     */
    private String roi;

    /**
     * 比例异常提示
     */
    private String proportionPrompt;

    /**
     * 本月末估值价格
     */
    private String monthEndValuationPrice;

    /**
     * 上月末估值价格
     */
    private String valuationPriceEndLastMonth;

    /**
     * 价格波动率
     */
    private String priceVolatility;

    /**
     * 波动率提示
     */
    private String volatilityAlert;

    /**
     * 付息日
     */
    private String interestPaymentDate;

    /**
     * 行权日
     */
    private String exerciseDate;

    /**
     * 到期日
     */
    private String dueDate;
}