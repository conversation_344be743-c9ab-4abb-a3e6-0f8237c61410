package cn.sdata.om.al.audit.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.sdata.om.al.mapper.ValuationDBMapper;
import cn.sdata.om.al.vo.MonthEndAndNonStandardVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.sdata.om.al.audit.entity.MonthEndAndNonStandard;
import cn.sdata.om.al.audit.mapper.MonthEndAndNonStandardMapper;
import cn.sdata.om.al.audit.service.MonthEndAndNonStandardService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【month_end_and_non_standard(月末存款及非标行情检查)】的数据库操作Service实现
* @createDate 2025-07-29 17:15:20
*/
@Service
@AllArgsConstructor
public class MonthEndAndNonStandardServiceImpl extends ServiceImpl<MonthEndAndNonStandardMapper, MonthEndAndNonStandard>
    implements MonthEndAndNonStandardService{

    private ValuationDBMapper valuationDBMapper;

    @Override
    public Page<MonthEndAndNonStandardVO> selectPageInfo(MonthEndAndNonStandardVO vo) {
        Page<MonthEndAndNonStandardVO> page = new Page<>(vo.getCurrent(), vo.getSize());
        this.baseMapper.selectPageInfo(page, vo);
        return page;
    }

    @Override
    public void syncMonthEndAndNonStandardInfo(String today) {

        //估值系统中的证券持仓表（可以取到账套下的账套持仓、类型、市场）
        List<MonthEndAndNonStandard> prodSecurityList = valuationDBMapper.selectProdSecurityList(today);
        Map<String, MonthEndAndNonStandard> prodSecurityMap = prodSecurityList.stream()
                .collect(Collectors.toMap(MonthEndAndNonStandard::getProductId, Function.identity()));


//        先用月末自然日去查询每个证券的估值价格，如果没有获取到的就用月末最后一个交易日进行查询。每月数据只有一个，查询到以后就退出下一次的查询
        //todo 根据数据日期获取本月末估值价格
        List<MonthEndAndNonStandard> thisMonth = valuationDBMapper.selectMonthEndAndNonStandardInfo(today);
        Map<String, MonthEndAndNonStandard> thisMonthMap = thisMonth.stream()
                .collect(Collectors.toMap(MonthEndAndNonStandard::getProductId, Function.identity()));
        //todo 根据数据日期获取上月末估值价格
        List<MonthEndAndNonStandard> lastMonth = valuationDBMapper.selectMonthEndAndNonStandardInfo(today);
        Map<String, MonthEndAndNonStandard> lastMonthMap = lastMonth.stream()
                .collect(Collectors.toMap(MonthEndAndNonStandard::getProductId, Function.identity()));

        //
        List<MonthEndAndNonStandard> list = valuationDBMapper.selectMonthEndAndNonStandardInfo(today);

        for (MonthEndAndNonStandard record : list) {

            MonthEndAndNonStandard monthEndAndNonStandard = prodSecurityMap.get(record.getProductId());
            record.setSecurityType(monthEndAndNonStandard.getSecurityType());

            BigDecimal roi = new BigDecimal(record.getSecurityMarketValue()).min(new BigDecimal(record.getSecurityCost()))
                    .divide(new BigDecimal(record.getSecurityCost()), 10, RoundingMode.HALF_UP);
            record.setRoi(roi.toPlainString());

            MonthEndAndNonStandard thisMonthInfo = thisMonthMap.get(record.getProductId());
            MonthEndAndNonStandard lastMonthInfo = lastMonthMap.get(record.getProductId());

            BigDecimal priceVolatility = new BigDecimal(thisMonthInfo.getMonthEndValuationPrice()).min(new BigDecimal(lastMonthInfo.getValuationPriceEndLastMonth()))
                    .divide(new BigDecimal(lastMonthInfo.getValuationPriceEndLastMonth()), 10, RoundingMode.HALF_UP);
            record.setPriceVolatility(priceVolatility.toPlainString());
        }

        if (CollUtil.isNotEmpty(list)){
            this.saveOrUpdateBatch(list);
        }

    }

}




