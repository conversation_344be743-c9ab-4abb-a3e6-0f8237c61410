package cn.sdata.om.al.audit.controller;


import cn.sdata.om.al.audit.service.MonthEndAndNonStandardService;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.vo.MonthEndAndNonStandardVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 月末存款及非标行情检查
 */
@RestController
@RequestMapping("/audit/month-end-and-non-standard")
@AllArgsConstructor
public class MonthEndAndNonStandardController {

    private MonthEndAndNonStandardService monthEndAndNonStandardService;

    /**
     * 月末存款及非标行情检查
     */
    @PostMapping("page")
    public R<Page<MonthEndAndNonStandardVO>> page(@RequestBody MonthEndAndNonStandardVO commonPageParam) {
        Page<MonthEndAndNonStandardVO> page = monthEndAndNonStandardService.selectPageInfo(commonPageParam);
        return R.ok(page);
    }


}
