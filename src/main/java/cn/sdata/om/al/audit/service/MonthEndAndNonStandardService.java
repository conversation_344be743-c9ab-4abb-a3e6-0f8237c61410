package cn.sdata.om.al.audit.service;

import cn.sdata.om.al.vo.MonthEndAndNonStandardVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import cn.sdata.om.al.audit.entity.MonthEndAndNonStandard;

/**
* <AUTHOR>
* @description 针对表【month_end_and_non_standard(月末存款及非标行情检查)】的数据库操作Service
* @createDate 2025-07-29 17:15:20
*/
public interface MonthEndAndNonStandardService extends IService<MonthEndAndNonStandard> {

    Page<MonthEndAndNonStandardVO> selectPageInfo(MonthEndAndNonStandardVO commonPageParam);

    void syncMonthEndAndNonStandardInfo(String today);
}
